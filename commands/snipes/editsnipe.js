const { EmbedBuilder } = require("discord.js");
const config = require('./../../config/setup');
const { embeds } = require('../../core/embeds');
const snipeCache = require('../../database/cache/snipe');

module.exports = {
  name: "editsnipe",
  aliases: ['es'],
  description: `snipes edited messages`,
  usage: '{guildprefix}editsnipe [number]',
  run: async(_, message, args) => {

    const editsnipes = snipeCache.getEditedMessages(message.channel.id);

    if (!editsnipes || editsnipes.length === 0) {
      return embeds.warn(message, `there's nothing to snipe in this channel`);
    }

    // Determine which editsnipe to show and prepare data
    let msg, footerText;

    if (args[0]) {
      const parsed = parseInt(args[0]);
      if (isNaN(parsed) || parsed < 1 || parsed > editsnipes.length) {
        return embeds.warn(message, `Please provide a valid number between 1 and ${editsnipes.length}`);
      }

      msg = editsnipes[parsed - 1];
      footerText = `${parsed}/${editsnipes.length} edits`;
    } else {
      // For no arguments, show the first editsnipe with text content
      const textEditSnipes = editsnipes.filter(msg => msg.oldContent && msg.oldContent.trim());

      if (textEditSnipes.length === 0) {
        return;
      }

      msg = textEditSnipes[0];
      footerText = `1/${textEditSnipes.length} edits`;
    }

    // Skip if the old content was only an image (no text)
    if (!msg.oldContent || !msg.oldContent.trim()) {
      return;
    }

    const embed = new EmbedBuilder()
      .setColor(config.colors.embed)
      .setAuthor({ name: `${msg.author}`, iconURL: msg.authorAvatar })
      .setDescription(msg.oldContent)
      .setFooter({ text: `${footerText} || edited at ${new Date(msg.timestamp).toLocaleString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      }).toLowerCase()}` })


    try {
      const targetMessage = await message.channel.messages.fetch(msg.messageId);
      return targetMessage.reply({ embeds: [embed] });
    } catch (error) {
      // Message was deleted or not found, send directly
      return message.channel.send({ embeds: [embed] });
    }
  }
}