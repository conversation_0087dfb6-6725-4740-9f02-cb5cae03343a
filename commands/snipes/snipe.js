const { EmbedBuilder } = require("discord.js");
const config = require('./../../config/setup');
const { embeds } = require('../../core/embeds');
const { createPagination } = require('../../core/buttons');
const snipeCache = require('../../database/cache/snipe');

module.exports = {
  name: "snipe",
  aliases: ['s'],
  description: `snipes deleted messages`,
  usage: '{guildprefix}snipe [number]',
  run: async(_, message, args) => {

    const snipes = snipeCache.getDeletedMessages(message.channel.id);

    if (!snipes || snipes.length === 0) {
      return embeds.warn(message, `there's nothing to snipe in this channel`);
    }

    if (args[0]) {
      const parsed = parseInt(args[0]);
      if (isNaN(parsed) || parsed < 1 || parsed > snipes.length) {
        return embeds.warn(message, `Please provide a valid number between 1 and ${snipes.length}`);
      }

      const msg = snipes[parsed - 1];
      const embed = new EmbedBuilder()
        .setColor(config.colors.embed)
        .setAuthor({ name: `${msg.author}`, iconURL: msg.authorAvatar })
        .setFooter({ text: `snipe ${parsed}/${snipes.length} || ${new Date(msg.timestamp).toLocaleString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        }).toLowerCase()}` })

      let description = '';
      if (msg.content && msg.content.trim()) {
        description = msg.content;
      }

      if (msg.attachments && msg.attachments.length > 0) {
        if (description) description += '\n\n';
        if (msg.attachments.length === 1) {
          description += `📎 **Media:** [View File](${msg.attachments[0]})`;
        } else {
          description += `📎 **Media (${msg.attachments.length} files):**\n`;
          description += msg.attachments.slice(0, 5).map((url, index) => `[File ${index + 1}](${url})`).join(' • ');
          if (msg.attachments.length > 5) {
            description += ` • +${msg.attachments.length - 5} more`;
          }
        }
      }

      if (description) {
        embed.setDescription(description);
      } else {
        embed.setDescription('*No content*');
      }

      if (msg.image) {
        embed.setImage(msg.image);
      }

      return message.channel.send({ embeds: [embed] });
    }

    const formatPage = (_, currentPage, totalPages) => {
      const msg = snipes[currentPage - 1];
      const embed = new EmbedBuilder()
        .setColor(config.colors.embed)
        .setAuthor({ name: `${msg.author}`, iconURL: msg.authorAvatar })
        .setFooter({ text: `deleted by ${message.author.username} || snipe ${currentPage}/${totalPages} || ${new Date(msg.timestamp).toLocaleString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        }).toLowerCase()}` })

      let description = '';
      if (msg.content && msg.content.trim()) {
        description = msg.content;
      }

      if (msg.attachments && msg.attachments.length > 0) {
        if (description) description += '\n\n';
        if (msg.attachments.length === 1) {
          description += `📎 **Media:** [View File](${msg.attachments[0]})`;
        } else {
          description += `📎 **Media (${msg.attachments.length} files):**\n`;
          description += msg.attachments.slice(0, 5).map((url, index) => `[File ${index + 1}](${url})`).join(' • ');
          if (msg.attachments.length > 5) {
            description += ` • +${msg.attachments.length - 5} more`;
          }
        }
      }

      if (description) {
        embed.setDescription(description);
      } else {
        embed.setDescription('*No content*');
      }

      if (msg.image) {
        embed.setImage(msg.image);
      }

      return embed;
    };

    await createPagination(message, snipes, formatPage, 1, 'Deleted Messages');
  }
}