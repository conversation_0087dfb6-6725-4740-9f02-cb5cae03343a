const { EmbedBuilder } = require('discord.js');
const config = require('../../../config/setup');
const { embeds } = require('../../../core/embeds');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { Commander } = require('./commander');

module.exports = {
  name: "ecmd",
  aliases: ['enable', 'enablecmd'],
  description: 'Enable commands or categories',
  usage: '{guildprefix}ecmd [command/category/path]\n{guildprefix}ecmd list',
  run: async(client, message, args) => {

    // Owner only - silently ignore if not owner
    if (message.author.id !== config.bot.ownerId) {
      return;
    }

    const cmdManager = new Commander();
    const input = args.join(' ').toLowerCase();

    if (!input) {
      return runHelpCommand(message, 'ecmd');
    }

    try {
      if (input === 'list') {
        return await showEnabledCommands(message, cmdManager);
      }

      // Check if input is a specific file path
      if (input.endsWith('.js')) {
        return await enableSpecificFile(message, cmdManager, input);
      }

      // Check if input is a folder path (contains /)
      if (input.includes('/')) {
        return await enableFolder(message, cmdManager, input);
      }

      // Check if input is a category
      const categories = cmdManager.getCategories();
      const category = categories.find(cat => cat.name.toLowerCase() === input);
      if (category) {
        return await enableCategory(message, cmdManager, input);
      }

      // Try to find command by name
      return await enableCommandByName(message, cmdManager, input);

    } catch (error) {

      return embeds.deny(message, `Error: ${error.message}`);
    }
  }
};

async function showEnabledCommands(message, cmdManager) {
  const commands = cmdManager.getEnabledCommands();

  if (commands.length === 0) {
    return embeds.warn(message, `There's no **Enabled Commands** currently`);
  }

  // Group by category
  const grouped = commands.reduce((groups, command) => {
    const category = command.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(command.name);
    return groups;
  }, {});

  let description = '**Currently Enabled Commands:**\n\n';
  for (const [category, cmds] of Object.entries(grouped)) {
    description += `**${category}:** ${cmds.join(', ')}\n`;
  }

  const embed = new EmbedBuilder()
    .setColor(config.colors.info)
    .setTitle('Enabled Commands')
    .setDescription(description)
    .setFooter({ text: `Total: ${commands.length} commands enabled` });

  return message.channel.send({ embeds: [embed] });
}



async function enableSpecificFile(message, cmdManager, filePath) {
  // Extract command name and category from file path
  const parts = filePath.replace('.js', '').split('/');
  const commandName = parts[parts.length - 1];
  const categoryPath = parts.slice(0, -1).join('/');

  try {
    await cmdManager.enableCommand(commandName, categoryPath);
    return embeds.success(message, `Enabled command **${commandName}** from **${categoryPath}**. Use \`,reload\` to apply changes.`);
  } catch (error) {
    return embeds.deny(message, error.message);
  }
}

async function enableFolder(message, cmdManager, folderPath) {
  const disabledCommands = cmdManager.getDisabledCommands();
  const commandsInFolder = disabledCommands.filter(cmd => 
    cmd.category.toLowerCase().startsWith(folderPath.toLowerCase())
  );

  if (commandsInFolder.length === 0) {
    return embeds.warn(message, `No **disabled commands** found in folder **${folderPath}**`);
  }

  let enabled = 0;
  let failed = 0;

  for (const command of commandsInFolder) {
    try {
      await cmdManager.enableCommand(command.name, command.category);
      enabled++;
    } catch (error) {
      failed++;

    }
  }

  return embeds.success(message, `Enabled **${enabled}** commands from **${folderPath}**${failed > 0 ? ` (${failed} failed)` : ''}. Use \`,reload\` to apply changes.`);
}

async function enableCategory(message, cmdManager, category) {
  try {
    const results = await cmdManager.enableCategory(category);
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    return embeds.success(message, `Enabled **${successful}** commands from category **${category}**${failed > 0 ? ` (${failed} failed)` : ''}. Use \`,reload\` to apply changes.`);
  } catch (error) {
    return embeds.deny(message, error.message);
  }
}

async function enableCommandByName(message, cmdManager, commandName) {
  const disabledCommands = cmdManager.getDisabledCommands();
  const matchingCommands = disabledCommands.filter(cmd => 
    cmd.name.toLowerCase() === commandName.toLowerCase()
  );

  if (matchingCommands.length === 0) {
    return embeds.warn(message, `Command **${commandName}** not found in disabled commands`);
  }

  if (matchingCommands.length === 1) {
    const command = matchingCommands[0];
    try {
      await cmdManager.enableCommand(command.name, command.category);
      return embeds.success(message, `Enabled command **${command.name}** from **${command.category}**. Use \`,reload\` to apply changes.`);
    } catch (error) {
      return embeds.deny(message, error.message);
    }
  }

  // Multiple matches - show options
  const options = matchingCommands.map((cmd, index) => 
    `\`${index + 1}\` **${cmd.name}** from **${cmd.category}**`
  ).join('\n');

  return embeds.warn(message, `Multiple **${commandName}** commands found:\n\n${options}\n\nUse: \`ecmd ${commandName} [category]\` to specify which one`);
}
