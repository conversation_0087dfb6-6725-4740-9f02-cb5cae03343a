const fs = require('fs');
const path = require('path');

class Commander {
    constructor() {
        this.commandsDir = path.join(__dirname, '../../../commands');
        this.disabledDir = path.join(__dirname, '../../../disabled');

        this.ensureDirectories();
    }

    ensureDirectories() {
        if (!fs.existsSync(this.commandsDir)) {
            fs.mkdirSync(this.commandsDir, { recursive: true });
        }
        if (!fs.existsSync(this.disabledDir)) {
            fs.mkdirSync(this.disabledDir, { recursive: true });
        }
    }

    getAllCommands() {
        const enabled = this.getEnabledCommands();
        const disabled = this.getDisabledCommands();
        
        return {
            enabled,
            disabled,
            total: enabled.length + disabled.length
        };
    }

    getEnabledCommands() {
        return this.scanDirectory(this.commandsDir);
    }

    getDisabledCommands() {
        return this.scanDirectory(this.disabledDir);
    }

    scanDirectory(dir) {
        const commands = [];

        if (!fs.existsSync(dir)) return commands;

        this.scanDirectoryRecursive(dir, dir, commands);
        return commands;
    }

    scanDirectoryRecursive(baseDir, currentDir, commands, categoryPath = '') {
        const items = fs.readdirSync(currentDir, { withFileTypes: true });

        for (const item of items) {
            const fullPath = path.join(currentDir, item.name);

            if (item.isDirectory()) {
                if (currentDir === baseDir) {
                    this.scanDirectoryRecursive(baseDir, fullPath, commands, item.name);
                } else {
                    const subPath = categoryPath ? `${categoryPath}/${item.name}` : item.name;
                    this.scanDirectoryRecursive(baseDir, fullPath, commands, subPath);
                }
            } else if (item.name.endsWith('.js')) {
                const category = categoryPath || path.relative(baseDir, path.dirname(fullPath));
                commands.push({
                    name: item.name.replace('.js', ''),
                    category: category,
                    file: item.name,
                    path: fullPath,
                    relativePath: path.relative(baseDir, fullPath)
                });
            }
        }
    }

    async enableCommand(commandName, categoryPath) {
        const disabledCommands = this.getDisabledCommands();
        const command = disabledCommands.find(cmd =>
            cmd.name === commandName && cmd.category === categoryPath
        );

        if (!command) {
            throw new Error(`Command ${commandName} not found in category ${categoryPath} in disabled commands`);
        }

        const disabledPath = command.path;
        const enabledPath = path.join(this.commandsDir, command.relativePath);

        const enabledDir = path.dirname(enabledPath);
        if (!fs.existsSync(enabledDir)) {
            fs.mkdirSync(enabledDir, { recursive: true });
        }

        fs.renameSync(disabledPath, enabledPath);
        return true;
    }

    async disableCommand(commandName, categoryPath) {
        const enabledCommands = this.getEnabledCommands();
        const command = enabledCommands.find(cmd =>
            cmd.name === commandName && cmd.category === categoryPath
        );

        if (!command) {
            throw new Error(`Command ${commandName} not found in category ${categoryPath} in enabled commands`);
        }

        const enabledPath = command.path;
        const disabledPath = path.join(this.disabledDir, command.relativePath);

        const disabledDir = path.dirname(disabledPath);
        if (!fs.existsSync(disabledDir)) {
            fs.mkdirSync(disabledDir, { recursive: true });
        }

        fs.renameSync(enabledPath, disabledPath);
        return true;
    }

    async enableCategory(category) {
        const disabledCategoryPath = path.join(this.disabledDir, category);
        
        if (!fs.existsSync(disabledCategoryPath)) {
            throw new Error(`Category ${category} not found in disabled commands`);
        }

        const files = fs.readdirSync(disabledCategoryPath)
            .filter(file => file.endsWith('.js'));

        const results = [];
        for (const file of files) {
            const commandName = file.replace('.js', '');
            try {
                await this.enableCommand(commandName, category);
                results.push({ command: commandName, success: true });
            } catch (error) {
                results.push({ command: commandName, success: false, error: error.message });
            }
        }

        return results;
    }

    async disableCategory(category) {
        const enabledCategoryPath = path.join(this.commandsDir, category);
        
        if (!fs.existsSync(enabledCategoryPath)) {
            throw new Error(`Category ${category} not found in enabled commands`);
        }

        const files = fs.readdirSync(enabledCategoryPath)
            .filter(file => file.endsWith('.js'));

        const results = [];
        for (const file of files) {
            const commandName = file.replace('.js', '');
            try {
                await this.disableCommand(commandName, category);
                results.push({ command: commandName, success: true });
            } catch (error) {
                results.push({ command: commandName, success: false, error: error.message });
            }
        }

        return results;
    }

    findCommand(commandName) {
        const enabled = this.getEnabledCommands();
        const disabled = this.getDisabledCommands();

        const enabledCmd = enabled.find(cmd => cmd.name === commandName);
        if (enabledCmd) {
            return { ...enabledCmd, status: 'enabled' };
        }

        const disabledCmd = disabled.find(cmd => cmd.name === commandName);
        if (disabledCmd) {
            return { ...disabledCmd, status: 'disabled' };
        }

        return null;
    }

    getCategories() {
        const enabledCategories = fs.existsSync(this.commandsDir) 
            ? fs.readdirSync(this.commandsDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name)
            : [];

        const disabledCategories = fs.existsSync(this.disabledDir)
            ? fs.readdirSync(this.disabledDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name)
            : [];

        const allCategories = [...new Set([...enabledCategories, ...disabledCategories])];
        
        return allCategories.map(category => ({
            name: category,
            enabled: enabledCategories.includes(category),
            disabled: disabledCategories.includes(category)
        }));
    }
}

module.exports = { Commander };
