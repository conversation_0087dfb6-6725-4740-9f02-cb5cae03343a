const snipeCache = require('../../database/cache/snipe');

module.exports = {
  name: 'messageUpdate',
  execute(oldMessage, newMessage, client) {

    if (!oldMessage.author || oldMessage.author.bot) return;
    snipeCache.addEditedMessage(oldMessage, newMessage);

    processEditedCommand(newMessage, client);
  },
};

async function processEditedCommand(message, client) {
  try {
    const { processCommand } = require('../../utils/commandProcessor');
    await processCommand(message, true);
  } catch (error) {

  }
}
