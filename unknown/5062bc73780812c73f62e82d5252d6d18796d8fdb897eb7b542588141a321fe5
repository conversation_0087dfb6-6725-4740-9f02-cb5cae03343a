const { ActivityType } = require('discord.js');

module.exports = {
  name: 'ready',
  once: true,
  execute(client) {
    // Set bot presence/status
    setPresence(client);
    
    // Update presence every 30 minutes
    setInterval(() => {
      setPresence(client);
    }, 30 * 60 * 1000);
  },
};

function setPresence(client) {
  const activities = [
    { name: `${client.guilds.cache.size} servers`, type: ActivityType.Watching },
    { name: `${client.users.cache.size} users`, type: ActivityType.Watching },
    { name: 'adore.bot', type: ActivityType.Playing },
    { name: 'with Discord.js', type: ActivityType.Playing },
    { name: 'music', type: ActivityType.Listening },
    { name: 'your commands', type: ActivityType.Listening },
  ];

  const randomActivity = activities[Math.floor(Math.random() * activities.length)];
  
  client.user.setPresence({
    activities: [randomActivity],
    status: 'online'
  });
}
